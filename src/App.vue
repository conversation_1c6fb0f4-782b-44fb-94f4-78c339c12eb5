<script lang="ts" setup>
import { $autoLogin } from '@/api/account'
import { useGlobalStore } from '@/stores/global'

onLaunch(() => {
  const globalStore = useGlobalStore()
  uni.login({
    provider: 'weixin',
    success: (loginRes: any) => {
      $autoLogin(loginRes.code).then((res: any) => {
        uni.setStorageSync('token', res.token)
        globalStore.token = res.token
        globalStore.handleRefreshUserInfo()
      })
    }
  })
})
onShow(() => {
  // console.log('App Show')
})
onHide(() => {
  // console.log('App Hide')
})
</script>

<style lang="scss">
@import 'tailwindcss/base';
@import 'tailwindcss/utilities';
@import '@/uni_modules/uview-plus/index.scss';
@import '@/uni_modules/uv-ui-tools/index.scss';
@import '@/static/scss/form.scss';

@import 'static/css/fontawesome/all.min.css';

page {
  background: #07011d;
}

/* toast提示 */
.u-toast {
  .u-toast__content {
    @apply pb-[12rpx] pl-[20rpx] pr-[20rpx] pt-[12rpx] !important;

    &.u-type-default {
      @apply bg-b-40 !important;
    }

    .u-icon {
      @apply mr-[8rpx];

      .u-icon__icon {
        @apply m-0 text-[36rpx] leading-[36rpx] !important;
      }
    }

    .u-toast__content__text {
      @apply break-all font-Regular text-[26rpx] font-normal leading-[36rpx] !important;
    }
  }
}

/* 空状态 */
.emptyWrap {
  min-height: 600rpx;

  .u-empty {
    .u-empty__text {
      margin-top: 20rpx !important;
      font-family: PingFangSC-Regular, 'PingFang SC';
      font-size: 26rpx !important;
      font-weight: 400;
      line-height: 36rpx;
      color: #fff !important;
      opacity: 0.6;
    }
  }

  &.mtPatch {
    .u-empty {
      .u-empty__text {
        margin-top: 58rpx !important;
      }
    }
  }

  &.mtPatch2 {
    .u-empty {
      .u-empty__text {
        margin-top: 33rpx !important;
      }
    }
  }
}

.emptyWrap2 {
  height: 600rpx;

  .u-empty {
    position: relative;

    .u-empty__text {
      position: absolute;
      bottom: 26rpx;
      margin-top: 0 !important;
      font-family: PingFangSC-Regular, 'PingFang SC';
      font-size: 26rpx !important;
      font-weight: 400;
      line-height: 36rpx;
      color: #fff !important;
      opacity: 0.6;
    }
  }
}

.emptyHidePic {
  .u-icon {
    display: none !important;
  }

  .u-empty__text {
    margin-top: 0 !important;
  }
}

/* 搜索框样式 */
.searchWrap {
  .searchBox {
    flex-grow: 1 !important;
    height: 100%;
    margin-left: 6rpx;

    &:deep(.u-input__content) {
      height: 100%;

      .u-input__content__field-wrapper {
        height: 100%;

        &__field {
          height: 100% !important;
          font-family: PingFangSC-Regular, 'PingFang SC';
          font-size: 26rpx !important;
          font-weight: 400;
          line-height: 64rpx !important;
          color: #fff !important;
        }

        .input-placeholder {
          font-family: PingFangSC-Regular, 'PingFang SC';
          font-size: 26rpx !important;
          font-weight: 400;
          line-height: 64rpx !important;
          color: rgb(255 255 255 / 60%) !important;
        }
      }
    }
  }
}

/* 轮播图背景 */
.swiperBg {
  background: transparent !important;
}

.u-image {
  .u-image__error {
    display: none !important;
    background-color: transparent !important;

    .u-icon {
      display: none !important;
    }
  }
}

/* 去除按钮默认样式 */
.btnDef {
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;

  &::after {
    display: none;
    content: none;
  }
}

/* 输入框清空样式 */
.u-input {
  .u-input__content {
    .u-input__content__clear {
      margin-left: 40rpx !important;
    }
  }
}

.u-navbar {
  .u-fixed {
    z-index: 50 !important;

    .u-navbar__content {
      z-index: 50 !important;
    }
  }
}
</style>
