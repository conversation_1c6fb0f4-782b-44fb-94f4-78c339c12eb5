/* 登录 */
export const $loginByWechat = (data: any) => uni.$u.http.post('/loginByWechat', data)

/*自动登录 */
export const $autoLogin = (code: string) => uni.$u.http.get('/autoLogin', { params: { code } })

/* 登录test */
export const $getWechatPhone = (data: any) => uni.$u.http.post('/getWechatPhone', data)

/* 获取用户信息 */
export const $getUserInfo = () => uni.$u.http.get('/user/getUserInfo')

/* 修改用户信息 */
export const $userUpdate = (data: any) => uni.$u.http.put('/user/update', data)

/* 修改用户密码（手机号码） */
export const $updatePwdByPhone = (data: any) => uni.$u.http.put('/user/updatePwdByPhone', data)

/* 修改用户密码（旧密码） */
export const $updatePwd = (data: any) => uni.$u.http.put('/user/updatePwd', data)

/* 修改用户手机号码 */
export const $updatePhone = (data: any) => uni.$u.http.put('/user/updatePhone', data)

/* 注销用户 */
export const $logout = (code: string) => uni.$u.http.delete('/user/logout', { code })
// export const $logout = () => uni.$u.http.delete('/user/logout')

/* 删除用户 */
export const $userDelete = () => uni.$u.http.delete('/user/delete')

/* 发送手机验证码 */
export const $sendUpdatePwdCode = () => uni.$u.http.get('/sendUpdatePwdCode')

/* 验证手机验证码 */
export const $verifyPwdCode = (code: string) => uni.$u.http.get('/verifyPwdCode', { params: { code } })

/* 发送修改手机号验证码(旧手机) */
export const $sendUpdatePhoneCode = () => uni.$u.http.get('/sendUpdatePhoneCode')

/* 验证重修改手机号验证码(旧手机) */
export const $verifyPhoneCode = (code: string) => uni.$u.http.get('/verifyPhoneCode', { params: { code } })

/* 发送修改手机号验证码(新号码) */
export const $sendUpdateNewPhoneCode = (phone: any) => uni.$u.http.get('/sendUpdateNewPhoneCode', { params: { phone } })

/* 发送注销手机号验证码 */
export const $sendLogoutCode = () => uni.$u.http.get('/sendLogoutCode')
