<template>
  <u-tabbar class="tabbar w-screen shrink-0" :border="false" :value="name" @change="handleLink" activeColor="#9C75D3" inactiveColor="#9592A0" zIndex="50">
    <!-- 首页 -->
    <u-tabbar-item name="Homepage" text="首页">
      <template #inactive-icon>
        <image class="h-[60rpx] w-[60rpx]" :src="$iconFormat('tabbar/icon1Off.webp')" webp />
      </template>
      <template #active-icon>
        <image class="h-[60rpx] w-[60rpx]" :src="$iconFormat('tabbar/icon1On.webp')" webp />
      </template>
    </u-tabbar-item>

    <!-- 日历 -->
    <u-tabbar-item name="Calendar" text="日历">
      <template #inactive-icon>
        <image class="h-[60rpx] w-[60rpx]" :src="$iconFormat('tabbar/icon2Off.webp')" webp />
      </template>
      <template #active-icon>
        <image class="h-[60rpx] w-[60rpx]" :src="$iconFormat('tabbar/icon2On.webp')" webp />
      </template>
    </u-tabbar-item>

    <!-- 扫描 -->
    <u-tabbar-item class="middleBtn relative z-10" name="Scan" text="传票根">
      <template #inactive-icon>
        <button class="absolute left-0 right-0 top-0 z-10 ml-auto mr-auto h-[160rpx] w-[118rpx] opacity-0" open-type="openSetting" v-if="showSetting"></button>
        <image class="h-[160rpx] w-[118rpx]" :src="$iconFormat('tabbar/middleIcon.webp')" ref="middleBtnRef" webp />
      </template>
      <template #active-icon>
        <button class="absolute left-0 right-0 top-0 z-10 ml-auto mr-auto h-[160rpx] w-[118rpx] opacity-0" open-type="openSetting" v-if="showSetting"></button>
        <image class="h-[160rpx] w-[118rpx]" :src="$iconFormat('tabbar/middleIcon.webp')" ref="middleBtnRef" webp />
      </template>
    </u-tabbar-item>
    <!-- 扫描按钮cover -->
    <!-- <u-transition :duration="500" :show="scanTypeController" mode="fade">
      <view class="cover absolute bottom-[100%] left-0 h-screen w-full bg-gradient-to-b from-b-0 to-b-100 opacity-90" @tap="handleShowScanBtn(false)"></view>
    </u-transition> -->
    <!-- 扫描纸质票 -->
    <!-- <view class="scanWrap absolute right-[50%] top-[-30rpx] translate-x-[50%] opacity-0" :animation="scanAni1" @tap.stop.prevent="handleScanPaper">
      <image class="m-auto mb-[10rpx] block h-[82rpx] w-[98rpx]" :src="$iconFormat('icon/scanPaper.svg')" mode="scaleToFill" />
      <text class="font-Regular text-xs font-normal leading-[34rpx] text-w-60">扫描纸质票</text>
    </view> -->
    <!-- 扫描二维码 -->
    <!-- <view class="scanWrap absolute left-[50%] top-[-30rpx] translate-x-[-50%] opacity-0" :animation="scanAni2" @tap.stop.prevent="handleScanQrCode">
      <image class="m-auto mb-[10rpx] block h-[84rpx] w-[82rpx]" :src="$iconFormat('icon/scanQrcode.svg')" mode="scaleToFill" />
      <text class="font-Regular text-xs font-normal leading-[34rpx] text-w-60">扫描二维码</text>
    </view> -->

    <!-- 商品 -->
    <u-tabbar-item name="CollectionList" text="商品">
      <template #inactive-icon>
        <image class="h-[60rpx] w-[60rpx]" :src="$iconFormat('tabbar/icon3Off.webp')" webp />
      </template>
      <template #active-icon>
        <image class="h-[60rpx] w-[60rpx]" :src="$iconFormat('tabbar/icon3On.webp')" webp />
      </template>
    </u-tabbar-item>

    <!-- 我的 -->
    <u-tabbar-item name="Personal" :badge="msgNum" :dot="!notifySetting.messageNotify && globalStore.token && msgNum > 0 ? true : false" text="我的">
      <template #inactive-icon>
        <image class="h-[60rpx] w-[60rpx]" :src="$iconFormat('tabbar/icon4Off.webp')" webp />
      </template>
      <template #active-icon>
        <image class="h-[60rpx] w-[60rpx]" :src="$iconFormat('tabbar/icon4On.webp')" webp />
      </template>
    </u-tabbar-item>
  </u-tabbar>
</template>

<script lang="ts">
export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
import { $dynamicCount, $findNotLookCount, $receivingRecordsCount, $userMessageNotifyCount, $userNotLookNotifyCount } from '@/api/count'
import { $userOrderNum } from '@/api/order'
import { $imgRecognition } from '@/api/scan'
import { $userSetting } from '@/api/setting'
import { useGlobalStore } from '@/stores/global'
import { $iconFormat, $loading, $push, $tab } from '@/utils/methods'

defineProps({
  name: { type: String, required: true } // 当前页面路由name
})

const app: any = getCurrentInstance()?.proxy
const globalStore = useGlobalStore()
const { userInfo } = storeToRefs(useGlobalStore())

const middleBtnRef = ref(null) // 中间扫码按钮

const scanTypeController = ref(false) // 扫描按钮显示控制器
const scanAni1 = ref() // 扫描按钮动画1
const scanAni2 = ref() // 扫描按钮动画2

const notifySetting = ref<any>('') // 消息设置
const msgNum = ref(0) // 消息数量
const showSetting = ref(false)

onShow(() => {
  uni.$off('updateTabbarNum')
  uni.$on('updateTabbarNum', handleGetNum)
  handleGetNum()

  uni.authorize({
    scope: 'scope.camera',
    success(res: any) {
      showSetting.value = false
    },
    fail(err) {
      showSetting.value = true
    }
  })
})

/* tabbar 跳转 */
const handleLink = (name: any) => {
  switch (name) {
    case 'Scan':
      if (!showSetting.value) $push({ name: 'Camera' })
      // handleScanPaper()
      // handleShowScanBtn(!scanTypeController.value)
      break

    default:
      // handleShowScanBtn(false)

      $tab({ name })
      break
  }
}

/* 显示扫码方式 */
const handleShowScanBtn = (status: boolean) => {
  scanTypeController.value = status
  handleScanBtnChangeAni()
}

/* 按钮动画 */
const handleScanBtnChangeAni = () => {
  let animation1 = uni.createAnimation({
    duration: 500,
    timingFunction: 'ease'
  })

  let animation2 = uni.createAnimation({
    duration: 500,
    timingFunction: 'ease'
  })

  nextTick(() => {
    if (scanTypeController.value) {
      animation1.opacity(1).top(uni.upx2px(-188)).translateX(uni.upx2px(-59)).step()
      scanAni1.value = animation1.export()

      animation2.opacity(1).top(uni.upx2px(-188)).translateX(uni.upx2px(59)).step()
      scanAni2.value = animation2.export()
    } else {
      animation1.opacity(0).top(uni.upx2px(-30)).translateX(uni.upx2px(60)).step()
      scanAni1.value = animation1.export()

      animation2.opacity(0).top(uni.upx2px(-30)).translateX(uni.upx2px(-60)).step()
      scanAni2.value = animation2.export()
    }
  })
}

/* 扫描纸质票 */
const handleScanPaper = () => {
  scanTypeController.value = false

  handleScanBtnChangeAni()
  globalStore.scanResult = ''

  uni.chooseMedia({
    count: 1,
    mediaType: ['image'],
    sourceType: ['album', 'camera'],
    success: (res: any) => {
      $loading('识别中')
      $imgRecognition({
        name: 'file',
        filePath: res.tempFiles[0].tempFilePath
      }).then((res: any) => {
        globalStore.scanResult = res
        uni.setStorageSync('scanResult', res)
        uni.hideLoading()
        $push({ name: 'ScanResult' })
      })
    }
  })
}

/* 扫描二维码 */
const handleScanQrCode = () => {
  scanTypeController.value = false
  handleScanBtnChangeAni()
  uni.scanCode({
    success: (res: any) => {
      let page: any = uni.$u.page()
      if (page === '/pages/sub/detail/repertoire') uni.redirectTo({ url: '/' + res.path })
      else uni.navigateTo({ url: '/' + res.path })
    }
  })
}

/* 消息数量统计 */
const handleGetNum = async () => {
  if (!globalStore.token) return

  notifySetting.value = (await $userSetting()).data

  // 电子票夹
  let num: number = (await $findNotLookCount(1)).data || 0
  // 数字头像
  num += (await $findNotLookCount(2)).data || 0
  // 纪念勋章
  num += (await $findNotLookCount(3)).data || 0
  // 推送消息
  num += (await $userNotLookNotifyCount()).data || 0
  // 我的关注
  num += (await $dynamicCount()).data || 0
  // 我的订单
  num += (await $userOrderNum()).data || 0
  // 我的评论
  // num += (await $replyByUserCount()).data
  // 纪念勋章
  num += (await $findNotLookCount(4)).data || 0
  // 观演历史
  // num += (await $commentCount({ userId: userInfo.value.id, commentFlag: 0 })).data|| 0
  // 互动消息
  num += (await $receivingRecordsCount(1)).data || 0
  num += (await $receivingRecordsCount(2)).data || 0
  num += (await $receivingRecordsCount(3)).data || 0
  // 群发消息
  num += (await $userMessageNotifyCount()).data || 0

  msgNum.value = num
}
</script>

<style lang="scss" scoped>
.tabbar {
  flex: initial !important;

  &:deep(.u-tabbar__content) {
    .u-tabbar__content__item-wrapper {
      position: relative;
      background-color: #07011d;

      .u-tabbar-item {
        &__text {
          margin: 0;
          font-family: PingFangSC-Regular, 'PingFang SC';
          font-size: 22rpx;
          font-weight: 400;
          line-height: 32rpx;
        }

        &.middleBtn {
          position: relative;

          .u-tabbar-item__icon {
            position: absolute;
            bottom: 44rpx;
          }

          .u-tabbar-item__text {
            margin-top: 60rpx;
          }
        }
      }
    }

    .u-safe-bottom {
      background-color: #07011d;
    }
  }
}
</style>
