<template>
  <view class="page-container">
    <mescroll-uni class="scroll-container" :down="downOpt1" :up="upOpt3" @down="downCallback" @init="mescrollInit" @scroll="handleScroll" @topclick="$topclick" @up="upCallback">
      <!-- 顶部一行：城市选择+AI搜索框 -->
      <view class="flex items-center justify-between w-full px-6 mb-4" :style="{ paddingTop: statusBarHeight + 8 + 'px' }">
        <!-- 左侧：城市选择 -->
        <view class="flex items-center" @tap="$push({ name: 'CitySelect' })">
          <text class="fas fa-map-marker-alt mr-2" :style="iconStyle"></text>
          <view class="line-clamp-1 w-[100rpx] font-Regular text-[26rpx] font-normal leading-[36rpx]" :style="primaryTextStyle">
            {{ addressInfo.name || currentCity }}
          </view>
        </view>

        <!-- 右侧：AI搜索框 -->
        <view class="search-container" @tap="aiSearchShow = true">
          <text class="fas fa-search search-icon"></text>
          <text class="search-placeholder">{{ searchPlaceholder }}</text>
        </view>
      </view>
      <!-- 我的榜单展示区 -->
      <view class="ranking-section">
        <view class="section-header">
          <view class="section-title-wrapper">
            <view class="section-icon">
              <text class="fas fa-trophy"></text>
            </view>
            <text class="section-title">我的榜单</text>
          </view>
          <view class="section-action" @tap="$push({ name: 'Rank' })">
            <text class="action-text">管理</text>
            <text class="fas fa-cog"></text>
          </view>
        </view>

        <!-- 榜单滑动容器 -->
        <scroll-view class="ranking-scroll" scroll-x="true" :show-scrollbar="false" :enable-flex="true">
          <view class="ranking-list">
            <!-- 榜单卡片 -->
            <view v-for="item in rankingData" :key="item.id" class="ranking-card" :class="{ 'empty-card': !item.repertoireName }" @tap="handleRankCardTap(item)">
              <!-- 榜单标题 -->
              <view class="rank-title-section">
                <view class="title-decoration">
                  <image class="title-icon" :src="$iconFormat('icon/titlePartl1.svg')" mode="scaleToFill" />
                  <text class="rank-title">{{ item.name }}</text>
                  <image class="title-icon" :src="$iconFormat('icon/titlePartr1.svg')" mode="scaleToFill" />
                </view>
              </view>

              <!-- 榜单内容 -->
              <view class="rank-content" v-if="item.coverPicture">
                <view class="rank-cover">
                  <image class="cover-image" :src="$picFormat(item.coverPicture)" mode="aspectFill" />
                  <view class="city-tag">{{ item.cityName }}</view>
                </view>
                <view class="rank-info">
                  <text class="show-name">{{ item.repertoireName }}</text>
                  <text class="theater-name">演出场地：{{ item.theaterName }}</text>
                  <text class="show-time">演出时间：{{ item.time }}</text>
                </view>
              </view>

              <!-- 空状态 -->
              <view class="rank-empty" v-else>
                <text class="fas fa-plus empty-plus"></text>
                <text class="empty-text">还未添加内容</text>
              </view>
            </view>

            <!-- 添加新榜单卡片 -->
            <view class="ranking-card add-card" v-if="canAddMore" @tap="$push({ name: 'RankEdit', params: { canSave: 1 } })">
              <view class="add-content">
                <view class="add-icon-wrapper">
                  <text class="fas fa-plus add-icon"></text>
                  <text class="add-text">设置新单项</text>
                </view>
                <text class="add-subtitle">还可创建{{ remainingCount }}个榜单</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 倒计时卡片区域 - 内联实现 -->
      <view class="countdown-display-inline">
        <!-- 主容器：包含开演倒计时和四个项目 -->
        <view class="main-countdown-card" @tap="handleCountdownSettingsClick">
          <!-- 背景装饰 -->
          <view class="bg-decoration-1"></view>
          <view class="bg-decoration-2"></view>

          <!-- 第一行：开演倒计时 -->
          <view v-if="mainCountdownData" class="countdown-header">
            <!-- 左侧：标题 -->
            <text class="countdown-title"></text>

            <!-- 中间：天数显示 -->
            <view class="days-container">
              <text class="days-unit">开演倒计时</text>
              <text class="days-number">{{ mainCountdownData.days }}</text>
              <text class="days-unit">天</text>
            </view>

            <!-- 右侧：设置按钮 -->
            <text class="fas fa-cog settings-icon"></text>
          </view>

          <!-- 空状态 -->
          <view v-else class="countdown-header">
            <text class="countdown-title">开演倒计时</text>
            <view class="empty-state">
              <text class="fas fa-clock empty-icon"></text>
              <text class="empty-text">点击设置</text>
            </view>
            <text class="fas fa-cog settings-icon"></text>
          </view>

          <!-- 第二行：四个项目并排显示 -->
          <view class="projects-grid">
            <!-- 发薪项目 -->
            <view class="project-item">
              <text class="project-label">发薪</text>
              <text class="project-value">{{ getInlineProjectValue('salary') }}</text>
            </view>

            <!-- 开票项目 -->
            <view class="project-item">
              <text class="project-label">开票</text>
              <text class="project-value">{{ getInlineProjectValue('ticketTime') }}</text>
            </view>

            <!-- 看剧基金项目 -->
            <view class="project-item">
              <text class="project-label">今天赚了</text>
              <text class="project-value">{{ getInlineProjectValue('fund') }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 足迹广场标题 -->
      <view class="footprint-header">
        <view class="section-title-wrapper">
          <view class="section-icon">
            <text class="fas fa-shoe-prints"></text>
          </view>
          <text class="section-title">足迹广场</text>
        </view>
        <!-- 排序选择器 -->
        <view class="sort-selector">
          <view class="sort-option" :class="{ active: sortType === 'hot' }" @tap="changeSortType('hot')">
            <text class="fas fa-fire sort-icon"></text>
          </view>
          <view class="sort-divider"></view>
          <view class="sort-option" :class="{ active: sortType === 'time' }" @tap="changeSortType('time')">
            <text class="fas fa-clock sort-icon"></text>
          </view>
        </view>
      </view>

      <!-- 瀑布流布局 - 与倒计时卡片宽度保持一致 -->
      <uv-waterfall
        :addTime="10"
        :style="{ minHeight: listH, paddingBottom: '120rpx' }"
        @changeList="handleChangeFootprintList"
        columnCount="2"
        columnGap="16rpx"
        columnWidth="auto"
        leftGap="24rpx"
        ref="footprintWaterfall"
        rightGap="24rpx"
        v-if="footprintList && footprintList.length > 0"
        v-model="footprintList"
        class="footprint-waterfall">
        <!-- 第一列数据 -->
        <template #list1>
          <FootprintItemNew :list="footprintList1" @updateList="list => (footprintList1 = list)" />
        </template>

        <!-- 第二列数据 -->
        <template #list2>
          <FootprintItemNew :list="footprintList2" @updateList="list => (footprintList2 = list)" />
        </template>
      </uv-waterfall>

      <!-- 空状态 -->
      <view class="empty-state" v-else style="padding-bottom: 120rpx">
        <view class="empty-illustration">
          <text class="empty-icon fas fa-theater-masks"></text>
        </view>
        <text class="empty-title">还没有足迹分享</text>
        <text class="empty-subtitle">快去观看演出，分享你的精彩体验吧</text>
        <view class="empty-action" @tap="$push({ name: 'ShowList' })">
          <text class="empty-button">探索演出</text>
        </view>
      </view>
    </mescroll-uni>

    <!-- 底部导航 -->
    <tabbar name="Homepage" />
  </view>

  <!-- AI搜索弹窗 -->
  <aiSearchDialog :show="aiSearchShow" @close="aiSearchShow = false" @search="handleAISearch" />

  <!-- toast提示 -->
  <u-toast ref="uToast"></u-toast>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
import aiSearchDialog from '../../components/Dialog/AISearchDialog.vue'
import FootprintItemNew from '../../components/Item/FootprintItemNew.vue'
import network from '../../components/Network.vue'
import tabbar from '../../components/Tabbar.vue'
import { useGlobalStore } from '@/stores/global'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import { $iconFormat, $picFormat, $push, $topclick } from '@/utils/methods'

const app: any = getCurrentInstance()?.proxy
const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)
const globalStore = useGlobalStore()
const { addressInfo, downOpt1, upOpt3 } = storeToRefs(globalStore)

// 页面数据
const statusBarHeight = ref(0)
const currentCity = ref('北京')
const searchPlaceholder = ref('搜索演出、剧院、艺术家...')
const aiSearchShow = ref(false)

// 问候语
const greetingText = computed(() => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了'
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
})

// 榜单数据 - 按照固定顺序：安利的、避雷的、怪东西，然后是其他
const rankingData = ref([
  {
    id: 1,
    name: '安利的',
    type: 1, // 1为用户榜单，0为系统榜单
    coverPicture: '', // 空状态
    cityName: '',
    repertoireName: '',
    theaterName: '',
    time: ''
  },
  {
    id: 2,
    name: '避雷的',
    type: 1,
    coverPicture: '', // 空状态
    cityName: '',
    repertoireName: '',
    theaterName: '',
    time: ''
  },
  {
    id: 3,
    name: '怪东西',
    type: 1,
    coverPicture: '', //
    cityName: '',
    repertoireName: '',
    theaterName: '',
    time: ''
  }
])

// 计算是否可以添加更多榜单
const canAddMore = computed(() => {
  const userRankCount = rankingData.value.filter(item => item.type === 1).length
  return userRankCount < 6 // 最多6个用户榜单
})

// 计算剩余可创建数量
const remainingCount = computed(() => {
  const userRankCount = rankingData.value.filter(item => item.type === 1).length
  return Math.max(0, 6 - userRankCount)
})

// 倒计时数据 - 符合CountdownDisplay组件的格式
const mainCountdownData = ref({
  title: '即将开演',
  days: 15,
  description: '距离演出还有',
  iconClass: 'fas fa-theater-masks'
})

const projectsData = ref({
  salary: { value: 15, unit: '天', enabled: true },
  ticketTime: { value: 3, unit: '天', enabled: true },
  fund: { value: '¥1,280', unit: '', enabled: true }
})

// 足迹数据 - 模拟数据（符合FootprintItemNew组件格式）
const mockFootprintData = [
  {
    id: 1,
    coverPicture: 'https://picsum.photos/300/400?random=1',
    imageHeight: '400rpx',
    showType: '话剧',
    repertoireName: '哈姆雷特',
    theaterName: '国家大剧院',
    content: '今晚看了《哈姆雷特》，莎士比亚的经典永远不会过时！演员的表演太精彩了，特别是独白部分，让人深深震撼。',
    user: {
      id: 1,
      nickname: '戏剧爱好者',
      avatar: 'https://picsum.photos/100/100?random=1'
    },
    userAvatar: 'https://picsum.photos/100/100?random=1',
    userName: '戏剧爱好者',
    showTime: '2025-01-15 19:30',
    likeCount: 128,
    commentCount: 23,
    isLiked: false,
    isPremium: true,
    createTime: '2024-01-15 20:30',
    showDate: '2024-01-15'
  },
  {
    id: 2,
    coverPicture: 'https://picsum.photos/300/500?random=4',
    imageHeight: '500rpx',
    showType: '音乐剧',
    repertoireName: '猫',
    theaterName: '上海大剧院',
    content: '《猫》音乐剧真的太棒了！每一首歌都让人印象深刻，舞台设计也很有创意。推荐大家一定要去现场感受！',
    user: {
      id: 2,
      nickname: '音乐剧迷',
      avatar: 'https://picsum.photos/100/100?random=3'
    },
    userAvatar: 'https://picsum.photos/100/100?random=3',
    userName: '音乐剧迷',
    showTime: '2025-01-14 20:00',
    likeCount: 89,
    commentCount: 15,
    isLiked: true,
    isPremium: false,
    createTime: '2024-01-14 22:15',
    showDate: '2024-01-14'
  },
  {
    id: 3,
    coverPicture: 'https://picsum.photos/300/350?random=6',
    imageHeight: '350rpx',
    showType: '话剧',
    repertoireName: '雷雨',
    theaterName: '人民艺术剧院',
    content: '第一次看话剧就被深深吸引了，演员的台词功底真的很扎实，整个剧情跌宕起伏，值得反复回味。',
    user: {
      id: 3,
      nickname: '话剧控',
      avatar: 'https://picsum.photos/100/100?random=5'
    },
    userAvatar: 'https://picsum.photos/100/100?random=5',
    userName: '话剧控',
    showTime: '2025-01-13 19:00',
    likeCount: 156,
    commentCount: 31,
    isLiked: false,
    isPremium: true,
    createTime: '2024-01-13 19:45',
    showDate: '2024-01-13'
  },
  {
    id: 4,
    coverPicture: 'https://picsum.photos/300/400?random=10',
    imageHeight: '400rpx',
    showType: '话剧',
    repertoireName: '恋爱的犀牛',
    theaterName: '蜂巢剧场',
    content: '小剧场话剧有种特别的魅力，演员和观众距离很近，能感受到更强烈的情感冲击。今天这部剧让我思考了很多。',
    user: {
      id: 4,
      nickname: '文艺青年',
      avatar: 'https://picsum.photos/100/100?random=9'
    },
    userAvatar: 'https://picsum.photos/100/100?random=9',
    userName: '文艺青年',
    showTime: '2025-01-12 14:30',
    likeCount: 67,
    commentCount: 12,
    isLiked: true,
    isPremium: false,
    createTime: '2024-01-12 21:20',
    showDate: '2024-01-12'
  },
  {
    id: 5,
    coverPicture: 'https://picsum.photos/300/600?random=12',
    imageHeight: '600rpx',
    showType: '话剧',
    repertoireName: '茶馆',
    theaterName: '首都剧场',
    content: '这是我今年看的第20部话剧了！每一部都有不同的感受，戏剧真的是一门伟大的艺术。',
    user: {
      id: 5,
      nickname: '剧院常客',
      avatar: 'https://picsum.photos/100/100?random=11'
    },
    userAvatar: 'https://picsum.photos/100/100?random=11',
    userName: '剧院常客',
    showTime: '2025-01-11 19:30',
    likeCount: 203,
    commentCount: 45,
    isLiked: false,
    isPremium: true,
    createTime: '2024-01-11 20:00',
    showDate: '2024-01-11'
  },
  {
    id: 6,
    coverPicture: 'https://picsum.photos/300/400?random=15',
    imageHeight: '400rpx',
    showType: '戏曲',
    repertoireName: '霸王别姬',
    theaterName: '梅兰芳大剧院',
    content: '传统戏曲的魅力无法言喻，每一个动作、每一句唱腔都蕴含着深厚的文化底蕴。希望更多年轻人能够了解和喜爱。',
    user: {
      id: 6,
      nickname: '戏曲爱好者',
      avatar: 'https://picsum.photos/100/100?random=14'
    },
    userAvatar: 'https://picsum.photos/100/100?random=14',
    userName: '戏曲爱好者',
    showTime: '2025-01-10 15:00',
    likeCount: 134,
    commentCount: 28,
    isLiked: true,
    isPremium: true,
    createTime: '2024-01-10 19:30',
    showDate: '2024-01-10'
  }
]

const footprintList = ref<any[]>([])
const footprintList1 = ref<any[]>([])
const footprintList2 = ref<any[]>([])

// 排序相关
const sortType = ref<'hot' | 'time'>('hot')

const listH = computed(() => {
  // 计算瀑布流的最小高度，确保有足够空间显示内容并避开底部导航栏
  const systemInfo = uni.$u.sys()
  const safeAreaHeight = systemInfo.safeArea.height
  const tabbarHeight = 120 // 底部导航栏高度（rpx转px约60px）
  const headerHeight = 200 // 顶部搜索和倒计时区域高度
  const minHeight = safeAreaHeight - headerHeight - tabbarHeight
  return uni.$u.addUnit(Math.max(minHeight, 400), 'px')
})

// 方法
const handleOpenAISearch = () => {
  aiSearchShow.value = true
}

const handleAISearch = (query: string) => {
  console.log('AI搜索:', query)
  aiSearchShow.value = false
}

// 内联倒计时组件相关函数
const handleCountdownSettingsClick = () => {
  uni.navigateTo({
    url: '/pages/sub/account/settings'
  })
}

const getInlineProjectValue = (projectKey: string) => {
  const project = (projectsData.value as any)?.[projectKey]
  if (!project || !project.enabled) {
    return '-'
  }

  if (projectKey === 'fund') {
    return `${project.value}${project.unit}`
  }

  return `${project.value}${project.unit}`
}

// 排序相关函数
const changeSortType = (type: 'hot' | 'time') => {
  sortType.value = type
  sortFootprintList()
}

const sortFootprintList = () => {
  let sortedList = [...mockFootprintData]

  if (sortType.value === 'hot') {
    // 按热度排序（点赞数 + 评论数）
    sortedList.sort((a, b) => {
      const scoreA = (a.likeCount || 0) + (a.commentCount || 0) * 2
      const scoreB = (b.likeCount || 0) + (b.commentCount || 0) * 2
      return scoreB - scoreA
    })
  } else {
    // 按时间倒序排序
    sortedList.sort((a, b) => {
      const timeA = new Date(a.createTime || a.showDate).getTime()
      const timeB = new Date(b.createTime || b.showDate).getTime()
      return timeB - timeA
    })
  }

  footprintList.value = sortedList

  // 重新分配到两列
  footprintList1.value = []
  footprintList2.value = []

  sortedList.forEach((item, index) => {
    if (index % 2 === 0) {
      footprintList1.value.push(item)
    } else {
      footprintList2.value.push(item)
    }
  })
}

// 样式计算属性 - 适配白色背景
const primaryTextStyle = computed(() => {
  return {
    color: '#1A1A1A'
  }
})

const secondaryTextStyle = computed(() => {
  return {
    color: '#666666'
  }
})

const iconStyle = computed(() => {
  return {
    fontSize: '40rpx',
    color: '#9333ea'
  }
})

const searchBoxStyle = computed(() => {
  return {
    backgroundColor: '#FFFFFF',
    borderRadius: '28rpx',
    boxShadow: '0 2rpx 16rpx rgba(0, 0, 0, 0.06)',
    border: '1px solid #F0F0F0'
  }
})

const searchIconStyle = computed(() => {
  return {
    fontSize: '28rpx',
    color: '#4A90E2'
  }
})

const searchTextStyle = computed(() => {
  return {
    color: '#999999'
  }
})

// 榜单卡片点击处理
const handleRankCardTap = (item: any) => {
  if (item.coverPicture) {
    // 有内容的榜单，跳转到详情页
    $push({ name: 'RankDetail', params: { id: item.id } })
  } else {
    // 空状态榜单，跳转到编辑页
    $push({ name: 'RankEdit', params: { id: item.id } })
  }
}

const getRankBadgeClass = (index: number) => {
  const classes = ['rank-first', 'rank-second', 'rank-third']
  return classes[index] || 'rank-other'
}

const handleChangeFootprintList = (e: any) => {
  switch (e.name) {
    case 'list1':
      footprintList1.value.push(e.value)
      break
    case 'list2':
      footprintList2.value.push(e.value)
      break
  }
}

const upCallback = (mescroll: any) => {
  // 上拉加载更多
  console.log('上拉加载更多')
  setTimeout(() => {
    // 模拟加载更多数据
    const newData = [...mockFootprintData].map((item, index) => ({
      ...item,
      id: item.id + footprintList.value.length,
      coverPicture: `https://picsum.photos/300/${300 + Math.random() * 300}?random=${Date.now() + index}`
    }))

    // 添加到现有数据
    footprintList.value.push(...newData)

    // 重新分配到两列
    newData.forEach((item, index) => {
      if ((footprintList.value.length + index) % 2 === 0) {
        footprintList1.value.push(item)
      } else {
        footprintList2.value.push(item)
      }
    })

    mescroll.endSuccess(newData.length)
  }, 1000)
}

const handleScroll = () => {
  // 滚动处理
}

onLoad(() => {
  statusBarHeight.value = uni.$u.sys().statusBarHeight || 0
  loadData()
})

const loadData = () => {
  // 使用模拟数据并应用排序
  sortFootprintList()
}
</script>

<style lang="scss" scoped>
@import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';

.page-container {
  height: 100vh;
  background: linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%);
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 搜索容器样式 */
.search-container {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 28rpx;
  padding: 12rpx 20rpx;
  flex: 1;
  max-width: 800rpx;
}

.search-icon {
  font-size: 24rpx;
  color: #999999;
  margin-right: 12rpx;
}

.search-placeholder {
  font-size: 24rpx;
  color: #999999;
  flex: 1;
}

.scroll-container {
  flex: 1;
  min-height: 0;
  position: relative;
}

/* CountdownDisplay组件兼容样式 */
.countdown-wrapper {
  width: 100%;
  position: relative;
  z-index: 10;

  /* 基础布局兼容 */
  :deep(.w-full) {
    width: 100% !important;
  }
  :deep(.px-6) {
    padding-left: 24rpx !important;
    padding-right: 24rpx !important;
  }
  :deep(.mb-6) {
    margin-bottom: 32rpx !important;
  }
  :deep(.mb-4) {
    margin-bottom: 16rpx !important;
  }
  :deep(.p-4) {
    padding: 16rpx !important;
  }
  :deep(.relative) {
    position: relative !important;
  }
  :deep(.absolute) {
    position: absolute !important;
  }
  :deep(.flex) {
    display: flex !important;
  }
  :deep(.items-center) {
    align-items: center !important;
  }
  :deep(.items-baseline) {
    align-items: baseline !important;
  }
  :deep(.justify-between) {
    justify-content: space-between !important;
  }
  :deep(.ml-2) {
    margin-left: 8rpx !important;
  }
  :deep(.mr-2) {
    margin-right: 8rpx !important;
  }
  :deep(.grid) {
    display: grid !important;
  }
  :deep(.grid-cols-4) {
    grid-template-columns: repeat(4, 1fr) !important;
  }
  :deep(.gap-2) {
    gap: 8rpx !important;
  }
  :deep(.text-center) {
    text-align: center !important;
  }
  :deep(.block) {
    display: block !important;
  }
  :deep(.overflow-hidden) {
    overflow: hidden !important;
  }
  :deep(.rounded-full) {
    border-radius: 50% !important;
  }
  :deep(.opacity-10) {
    opacity: 0.1 !important;
  }
  :deep(.opacity-5) {
    opacity: 0.05 !important;
  }
  :deep(.bg-white) {
    background-color: #ffffff !important;
  }
  :deep(.bg-black) {
    background-color: #000000 !important;
  }
  :deep(.bg-opacity-30) {
    background-color: rgba(0, 0, 0, 0.3) !important;
  }
  :deep(.z-10) {
    z-index: 10 !important;
  }

  /* 渐变背景兼容 */
  :deep(.bg-gradient-to-br) {
    background: linear-gradient(135deg, #9333ea 0%, #2563eb 100%) !important;
  }

  /* 圆角兼容 */
  :deep([class*='rounded-']) {
    border-radius: 24rpx !important;
  }
}

/* 内联倒计时组件样式 */
.countdown-display-inline {
  width: 100%;
  padding: 0 24rpx;
  margin-bottom: 32rpx;
}

.main-countdown-card {
  position: relative;
  background: linear-gradient(135deg, #9333ea 0%, #5b1d94 100%);
  border-radius: 24rpx;
  padding: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(124, 58, 237, 0.3);
  transition: transform 0.1s ease;

  &:active {
    transform: scale(0.98);
  }
}

/* 背景装饰 */
.bg-decoration-1 {
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.bg-decoration-2 {
  position: absolute;
  bottom: -30rpx;
  left: -30rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
}

/* 倒计时头部 */
.countdown-header {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.countdown-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #ffffff;
}

.days-container {
  display: flex;
  align-items: baseline;
}

.days-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #ffffff;
}

.days-unit {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-left: 8rpx;
  margin-right: 8rpx;
}

.settings-icon {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
}

.empty-icon {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-right: 8rpx;
}

.empty-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}

/* 项目网格 */
.projects-grid {
  position: relative;
  z-index: 10;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8rpx;
}

.project-item {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;
  padding: 8rpx;
  text-align: center;
  min-height: 60rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: transform 0.1s ease;

  &:active {
    transform: scale(0.95);
  }
}

.project-label {
  font-size: 18rpx;
  color: #ffffff;
  margin-bottom: 4rpx;
}

.project-value {
  font-size: 22rpx;
  font-weight: 500;
  color: #ffffff;
}

/* 通用区域样式 */
.ranking-section {
  margin: 0 24rpx 0rpx 24rpx;
  //background: #ffffff;
  //border-radius: 24rpx;
  padding: 32rpx 0rpx;
  //box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
  //border: 1px solid #f5f5f5;
}

/* 足迹广场标题样式 */
.footprint-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  margin-bottom: 16rpx;
  margin-top: 16rpx;
}

/* 排序选择器样式 */
.sort-selector {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.sort-option {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  cursor: pointer;
}

.sort-icon {
  font-size: 24rpx;
  color: #999999;
  transition: color 0.3s ease;

  .sort-option.active & {
    color: #9333ea;
  }
}

/* 瀑布流样式 */
.footprint-waterfall {
  width: 100%;
  min-height: 400rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
}

.section-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #9840ea 0%, #764ba2 100%);
  border-radius: 12rpx;
  margin-right: 16rpx;

  .fas {
    font-size: 24rpx;
    color: #ffffff;
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 40rpx;
}

.section-action {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: #f1f1f1;
  border-radius: 16rpx;

  .action-text {
    font-size: 24rpx;
    color: #666666;
    margin-right: 8rpx;
  }

  .fas {
    font-size: 20rpx;
    color: #666666;
  }
}

/* 榜单滑动区域样式 */
.ranking-scroll {
  width: 100%;
  white-space: nowrap;
}

.ranking-list {
  display: flex;
  gap: 16rpx;
  padding: 0 0rpx;
}

.ranking-card {
  flex-shrink: 0;
  width: 280rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1px solid #f5f5f5;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  }

  /* 空状态卡片的毛玻璃效果 */
  &.empty-card {
    background: rgba(147, 51, 234, 0.15);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    border: 1px solid rgba(147, 51, 234, 0.2);
    box-shadow: 0 4rpx 20rpx rgba(147, 51, 234, 0.1);

    &:active {
      transform: translateY(-4rpx);
      box-shadow: 0 8rpx 32rpx rgba(147, 51, 234, 0.2);
    }
  }
}

/* 榜单标题区域 */
.rank-title-section {
  margin-bottom: 16rpx;
}

.title-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}

.title-icon {
  width: 24rpx;
  height: 24rpx;
  margin: 0 8rpx;
}

.rank-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
  text-align: center;
}

/* 榜单内容区域 */
.rank-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.rank-cover {
  position: relative;
  width: 100%;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.city-tag {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  padding: 4rpx 8rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  font-size: 18rpx;
  border-radius: 8rpx;
  backdrop-filter: blur(10rpx);
}

.rank-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.show-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 32rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.theater-name,
.show-time {
  font-size: 20rpx;
  color: #666666;
  line-height: 26rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

/* 榜单空状态 */
.rank-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 240rpx; /* 增加高度确保垂直居中 */
  text-align: center;
  padding: 20rpx;
}

.empty-plus {
  font-size: 54rpx; /* 增大图标 */
  color: #cccccc;
  margin-bottom: 20rpx;
  transition: color 0.3s ease;
}

.rank-empty .empty-text {
  font-size: 22rpx;
  color: #999999;
  line-height: 28rpx;
  text-align: center;
  max-width: 200rpx; /* 限制文字宽度，确保换行 */
  transition: color 0.3s ease;
}

/* 空状态卡片中的内容颜色调整 */
.empty-card {
  .empty-plus {
    color: rgba(147, 51, 234, 0.6);
  }

  .rank-empty .empty-text {
    color: rgba(147, 51, 234, 0.8);
    font-weight: 500;
  }
}

/* 添加新榜单卡片 */
.add-card {
  border: 2px dashed #cccccc;
  background: #fafbfc;
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
}

.add-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 240rpx; /* 与空状态保持一致的高度 */
  text-align: center;
  padding: 20rpx;
}

.add-icon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16rpx;
}

.add-icon {
  font-size: 54rpx; /* 与空状态图标大小一致 */
  color: #9333ea;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #9333ea;
  margin-bottom: 4rpx;
}

.add-subtitle {
  font-size: 20rpx;
  color: #999999;
  line-height: 26rpx;
  text-align: center;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-illustration {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  border-radius: 50%;
  margin-bottom: 32rpx;
  border: 1px solid #e1eaff;
}

.empty-icon {
  font-size: 64rpx;
  color: #4a90e2;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 40rpx;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #666666;
  line-height: 36rpx;
  margin-bottom: 40rpx;
}

.empty-action {
  margin-top: 16rpx;
}

.empty-button {
  display: inline-block;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #9c49ea 0%, #764ba2 100%);
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

/* 交互效果 */
.city-selector,
.search-box,
.section-action,
.empty-button {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.search-box:active {
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
}

.empty-button:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .ranking-item {
    padding: 16rpx 20rpx;
  }

  .rank-badge {
    width: 40rpx;
    height: 40rpx;
    margin-right: 16rpx;
  }

  .rank-number {
    font-size: 20rpx;
  }

  .rank-title {
    font-size: 26rpx;
  }

  .stats-number {
    font-size: 28rpx;
  }
}

/* 深色模式适配（预留） */
@media (prefers-color-scheme: dark) {
  .page-container {
    background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
  }

  .header-section {
    background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
  }

  .greeting-main {
    color: #ffffff;
  }

  .greeting-sub {
    color: #cccccc;
  }

  .section-title {
    color: #ffffff;
  }
}
</style>
