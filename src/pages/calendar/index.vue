<template>
  <view class="page-container">
    <u-navbar class="navbar w-full shrink-0" bgColor="#ffffff" leftIcon=" " placeholder>
      <template #left>
        <view class="nav-header">
          <view class="nav-left">
            <!-- <text class="fas fa-chevron-left nav-back-icon" @click="goBack"></text> -->
            <text class="nav-title">我的日历</text>
          </view>
          <view class="nav-right">
            <!-- 看剧/物料切换 - 分段控制器样式 -->
            <view class="calendar-mode-switch">
              <view class="segmented-control">
                <view class="segment-option" :class="{ active: currentMode === 'show' }" @click="switchToMode('show')">
                  <!-- <view class="segment-icon">
                    <text class="fas fa-theater-masks"></text>
                  </view> -->
                  <text class="segment-text">看剧</text>
                </view>
                <view class="segment-option" :class="{ active: currentMode === 'material' }" @click="switchToMode('material')">
                  <!-- <view class="segment-icon">
                    <text class="fas fa-box-open"></text>
                  </view> -->
                  <text class="segment-text">物料</text>
                </view>
                <view class="segment-indicator" :class="{ 'move-right': currentMode === 'material' }"></view>
              </view>
            </view>
            <!-- <text class="fas fa-plus nav-icon" @click="addMaterial"></text>
            <text class="fas fa-cog nav-icon" @click="openSettings"></text> -->
          </view>
        </view>
      </template>
    </u-navbar>

    <!-- 控制栏：标题、视图切换、翻页 -->
    <view class="control-section">
      <view class="control-header">
        <view class="title-section">
          <text class="current-title">{{ getViewTitle() }}</text>
          <text class="current-subtitle">{{ getViewSubtitle() }}</text>
        </view>

        <view class="control-actions">
          <!-- 视图切换按钮 -->
          <view class="view-switch">
            <view class="view-option" :class="{ active: calendarView === 'month' }" @click="switchView('month')">
              <text class="fas fa-calendar-day view-icon"></text>
              <text class="view-text">月</text>
            </view>
            <view class="view-option" :class="{ active: calendarView === 'year' }" @click="switchView('year')">
              <text class="fas fa-calendar-alt view-icon"></text>
              <text class="view-text">年</text>
            </view>
            <view class="view-option" :class="{ active: calendarView === 'decade' }" @click="switchView('decade')">
              <text class="fas fa-calendar view-icon"></text>
              <text class="view-text">10年</text>
            </view>
          </view>

          <!-- 翻页按钮 -->
          <view class="nav-buttons">
            <view class="nav-btn prev" @click="previousPeriod">
              <text class="fas fa-chevron-left"></text>
            </view>
            <view class="nav-btn next" @click="nextPeriod">
              <text class="fas fa-chevron-right"></text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 统计信息卡片 - 独立展示 -->
    <view v-if="currentMode === 'show'" class="stats-section">
      <view class="stats-card">
        <view class="stats-header">
          <view class="stats-icon">
            <text class="fas fa-chart-bar"></text>
          </view>
          <text class="stats-title">{{ getStatsTitle() }}</text>
          <view class="amount-toggle" @click="toggleAmountDisplay">
            <text class="fas" :class="showAmount ? 'fa-eye' : 'fa-eye-slash'"></text>
          </view>
        </view>

        <view class="stats-content">
          <view class="stats-item primary">
            <view class="stats-value">
              <text class="stats-number">{{ calculatedStats.totalShows || 0 }}</text>
              <text class="stats-unit">场</text>
            </view>
            <text class="stats-label">观演场次</text>
          </view>

          <view v-if="showAmount" class="stats-item secondary">
            <view class="stats-value">
              <text class="stats-number">¥{{ formatAmount(calculatedStats.totalAmount || 0) }}</text>
            </view>
            <text class="stats-label">票房贡献</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 日历主体 -->
    <view class="calendar-container">
      <!-- 月视图 -->
      <view v-if="calendarView === 'month'" class="month-view">
        <!-- 星期标题 -->
        <view class="weekdays-header">
          <view v-for="day in weekDays" :key="day" class="weekday-item">
            <text class="weekday-text">{{ day }}</text>
          </view>
        </view>

        <!-- 日历网格 -->
        <view class="calendar-grid">
          <view v-for="week in calendarWeeks" :key="week.id" class="calendar-week">
            <view v-for="day in week.days" :key="day.date || `empty-${week.id}-${day.day}`" class="calendar-day" :class="getDayClass(day)" @click="day.isCurrentMonth ? selectDate(day) : null">
              <!-- 日期数字 -->
              <view v-if="day.day > 0" class="day-number">
                <text class="day-text" :class="{ 'current-month': day.isCurrentMonth, 'other-month': !day.isCurrentMonth }">
                  {{ day.day }}
                </text>
              </view>

              <!-- 看剧模式：显示剧目海报 -->
              <view v-if="currentMode === 'show' && day.shows?.length" class="show-content">
                <!-- 单个剧目：满铺整个格子 -->
                <view v-if="day.shows.length === 1" class="single-show">
                  <image :src="day.shows[0].poster" class="show-poster full" mode="aspectFill" />
                </view>

                <!-- 多个剧目：上下平分 -->
                <view v-else class="multiple-shows">
                  <view v-for="(show, index) in day.shows.slice(0, 2)" :key="show.id" class="show-item" :class="{ top: index === 0, bottom: index === 1 }">
                    <image :src="show.poster" class="show-poster split" mode="aspectFill" />
                  </view>
                  <!-- 更多剧目指示 -->
                  <view v-if="day.shows.length > 2" class="more-shows">
                    <text class="more-text">+{{ day.shows.length - 2 }}</text>
                  </view>
                </view>
              </view>

              <!-- 物料模式：简单标记 -->
              <view v-if="currentMode === 'material' && day.materials?.length" class="material-mark">
                <view class="material-dot"></view>
                <text class="material-count">{{ day.materials.length }}</text>
              </view>

              <!-- 空白日期的微妙装饰 -->
              <view v-if="day.day > 0 && day.isCurrentMonth && !((currentMode === 'show' && day.shows?.length) || (currentMode === 'material' && day.materials?.length))" class="empty-day-decoration">
                <view class="decoration-dot"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 年视图 -->
      <view v-if="calendarView === 'year'" class="year-view">
        <view class="year-grid">
          <view v-for="month in 12" :key="month" class="month-card" @click="selectMonth(month)">
            <view class="month-header">
              <view class="month-title-section">
                <text class="month-title">{{ month }}月</text>
                <view class="month-indicator" :class="{ current: isCurrentMonth(month) }">
                  <text v-if="isCurrentMonth(month)" class="fas fa-circle"></text>
                </view>
              </view>
            </view>

            <!-- 月份小日历 - 更精美的设计 -->
            <view class="month-calendar">
              <view class="mini-calendar-grid">
                <view
                  v-for="day in getMonthDays(month)"
                  :key="day.date || `empty-${month}-${day.day}`"
                  class="mini-day"
                  :class="{
                    'has-show': currentMode === 'show' && day.hasShow,
                    'has-material': currentMode === 'material' && day.hasMaterial,
                    'current-month': day.isCurrentMonth,
                    today: isToday(day)
                  }">
                  <text v-if="day.day > 0" class="mini-day-text">{{ day.day }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 十年视图 -->
      <view v-if="calendarView === 'decade'" class="decade-view">
        <view class="decade-grid">
          <view v-for="year in getDecadeYears()" :key="year" class="year-card" @click="selectYear(year)">
            <view class="year-header">
              <text class="year-title">{{ year }}</text>
              <view v-if="isCurrentYear(year)" class="current-year-indicator">
                <text class="fas fa-star"></text>
              </view>
            </view>

            <!-- 月份网格 -->
            <view class="year-months">
              <view class="months-grid">
                <view
                  v-for="month in 12"
                  :key="month"
                  class="month-item"
                  :class="{
                    'has-activity': (currentMode === 'show' && getMonthStats(month, year).shows > 0) || (currentMode === 'material' && Math.random() > 0.7),
                    current: isCurrentYear(year) && isCurrentMonth(month)
                  }">
                  <text class="month-text">{{ month }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 选中日期详情 -->
    <view v-if="selectedDay" class="selected-day-details">
      <view class="details-header">
        <text class="details-date">{{ selectedDay.dateStr }}</text>
        <view class="close-btn" @click="selectedDay = null">
          <text class="fas fa-times close-icon"></text>
        </view>
      </view>

      <!-- 看剧模式详情 -->
      <view v-if="currentMode === 'show' && selectedDay.shows?.length">
        <view v-for="show in selectedDay.shows" :key="show.id" class="show-item-detail">
          <image :src="show.poster" class="show-poster-detail" mode="aspectFill" />
          <view class="show-info">
            <text class="show-title">{{ show.title }}</text>
            <view class="show-meta">
              <text class="show-theater">{{ show.theater }}</text>
            </view>
            <view class="show-meta-row">
              <text class="show-time">{{ show.time }}</text>
              <text class="show-price">¥{{ show.price }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 物料模式详情 -->
      <view v-if="currentMode === 'material' && selectedDay.materials?.length">
        <view v-for="material in selectedDay.materials" :key="material.id" class="material-item-detail" @click="viewMaterial(material)">
          <image :src="material.image" class="material-image-detail" mode="aspectFill" />
          <view class="material-info">
            <text class="material-title">{{ material.title }}</text>
            <view class="material-meta">
              <text class="material-location">{{ material.location }}</text>
            </view>
            <view class="material-footer">
              <text class="want-count">{{ material.wantCount }}人想领</text>
              <text class="fas fa-chevron-right material-arrow"></text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态提示 -->
      <view v-if="currentMode === 'show' && (!selectedDay.shows || selectedDay.shows.length === 0)" class="empty-state">
        <text class="empty-text">该日期暂无演出</text>
      </view>

      <view v-if="currentMode === 'material' && (!selectedDay.materials || selectedDay.materials.length === 0)" class="empty-state">
        <text class="empty-text">该日期暂无物料</text>
      </view>
    </view>

    <!-- 分享按钮 -->
    <view v-if="currentMode === 'show'" class="fixed bottom-32 right-4">
      <view class="w-12 h-12 bg-primary rounded-full flex items-center justify-center" @click="shareCalendar">
        <text class="fas fa-share" style="font-size: 20rpx; color: #fff"></text>
      </view>
    </view>

    <!-- 底部导航 -->
    <tabbar name="Calendar" />
  </view>
</template>

<script lang="ts" setup>
import tabbar from '@/components/Tabbar.vue'
import { onLoad } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'

interface Show {
  id: string
  watched: boolean
  title: string
  theater: string
  time: string
  poster: string
  price: number
}

interface Material {
  id: string
  title: string
  location: string
  wantCount: number
  image: string
}

interface DayData {
  date: string
  day: number
  isCurrentMonth: boolean
  dateStr: string
  shows: Show[]
  materials: Material[]
}

// 状态管理
const statusBarHeight = ref(0)
const currentMode = ref<'show' | 'material'>('show')
const selectedDay = ref<DayData | null>(null)
const currentDate = ref(new Date())
const showAmount = ref(true) // 控制是否显示金额
const calendarView = ref<'month' | 'year' | 'decade'>('month') // 日历视图模式

const weekDays = ['日', '一', '二', '三', '四', '五', '六']

// 计算属性
const calendarWeeks = computed(() => {
  // 生成日历数据的逻辑
  return generateCalendarData()
})

// 计算统计数据 - 根据当前视图和模式计算
const calculatedStats = computed(() => {
  let totalShows = 0
  let totalAmount = 0

  switch (calendarView.value) {
    case 'month':
      // 月视图：计算当月数据
      const weeks = calendarWeeks.value
      weeks.forEach(week => {
        week.days.forEach(day => {
          if (day.isCurrentMonth && currentMode.value === 'show' && day.shows && day.shows.length > 0) {
            totalShows += day.shows.length
            // 计算票房：每场演出基础票价350元
            day.shows.forEach(() => {
              totalAmount += 350
            })
          }
        })
      })
      break

    case 'year':
      // 年视图：计算全年数据
      for (let month = 1; month <= 12; month++) {
        const monthStats = getMonthStats(month)
        totalShows += monthStats.shows
        totalAmount += monthStats.amount
      }
      break

    case 'decade':
      // 十年视图：计算十年数据
      const decadeYears = getDecadeYears()
      decadeYears.forEach(year => {
        const yearStats = getYearStats(year)
        totalShows += yearStats.shows
        totalAmount += yearStats.amount
      })
      break
  }

  return {
    totalShows,
    totalAmount
  }
})

// 方法
const goBack = () => {
  uni.navigateBack()
}

// 模式切换方法
const toggleMode = () => {
  currentMode.value = currentMode.value === 'show' ? 'material' : 'show'
}

// 新的模式切换方法 - 支持直接指定模式
const switchToMode = (mode: 'show' | 'material') => {
  if (currentMode.value !== mode) {
    currentMode.value = mode
    // 添加触觉反馈
    // uni.vibrateShort({
    //   type: 'light'
    // })
  }
}

const addMaterial = () => {
  uni.navigateTo({
    url: '/pages/sub/calendar/add-material'
  })
}

const viewMaterial = (material: Material) => {
  uni.navigateTo({
    url: `/pages/sub/calendar/material-detail?id=${material.id}`
  })
}

const shareCalendar = () => {
  // 分享功能实现
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none'
  })
}

const generateCalendarData = () => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)

  // 只显示当月日期，不显示其他月份的日期
  const weeks = []
  let currentWeek = []

  // 添加月初前的空白天数
  for (let i = 0; i < firstDay.getDay(); i++) {
    currentWeek.push({
      date: '',
      day: 0,
      isCurrentMonth: false,
      dateStr: '',
      shows: [],
      materials: []
    })
  }

  // 添加当月的所有日期
  for (let day = 1; day <= lastDay.getDate(); day++) {
    const date = new Date(year, month, day)

    const dayData: DayData = {
      date: date.toISOString().split('T')[0],
      day: day,
      isCurrentMonth: true,
      dateStr: `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
      shows: getMockShowsForDate(date),
      materials: getMockMaterialsForDate(date)
    }

    currentWeek.push(dayData)

    // 每7天组成一周
    if (currentWeek.length === 7) {
      weeks.push({ id: weeks.length, days: currentWeek })
      currentWeek = []
    }
  }

  // 如果最后一周不满7天，用空白填充
  while (currentWeek.length > 0 && currentWeek.length < 7) {
    currentWeek.push({
      date: '',
      day: 0,
      isCurrentMonth: false,
      dateStr: '',
      shows: [],
      materials: []
    })
  }

  // 添加最后一周（如果有的话）
  if (currentWeek.length > 0) {
    weeks.push({ id: weeks.length, days: currentWeek })
  }

  return weeks
}

// 生成模拟看剧数据 - 便于后续替换为真实API
const getMockShowsForDate = (date: Date): Show[] => {
  const dayOfMonth = date.getDate()
  const shows: Show[] = []

  // 根据日期模拟不同的演出数据
  if (dayOfMonth % 5 === 0) {
    shows.push({
      id: `show_${dayOfMonth}_1`,
      watched: Math.random() > 0.5,
      title: '狮子王',
      theater: '上海大剧院',
      time: '19:30',
      poster: 'https://picsum.photos/300/400?random=' + dayOfMonth,
      price: 350
    })
  }

  if (dayOfMonth % 7 === 0) {
    shows.push({
      id: `show_${dayOfMonth}_2`,
      watched: Math.random() > 0.3,
      title: '歌剧魅影',
      theater: '上海文化广场',
      time: '20:00',
      poster: 'https://picsum.photos/300/400?random=' + (dayOfMonth + 100),
      price: 280
    })
  }

  if (dayOfMonth % 3 === 0 && dayOfMonth % 6 !== 0) {
    shows.push({
      id: `show_${dayOfMonth}_3`,
      watched: Math.random() > 0.7,
      title: '芝加哥',
      theater: '北京天桥艺术中心',
      time: '14:00',
      poster: 'https://picsum.photos/300/400?random=' + (dayOfMonth + 200),
      price: 420
    })
  }

  return shows
}

// 生成模拟物料数据 - 便于后续替换为真实API
const getMockMaterialsForDate = (date: Date): Material[] => {
  const dayOfMonth = date.getDate()
  const materials: Material[] = []

  // 根据日期模拟不同的物料数据
  if (dayOfMonth % 4 === 0) {
    materials.push({
      id: `material_${dayOfMonth}_1`,
      title: '《狮子王》限量版海报',
      location: '上海大剧院门厅',
      wantCount: Math.floor(Math.random() * 100) + 20,
      image: 'https://picsum.photos/200/200?random=' + (dayOfMonth + 300)
    })
  }

  if (dayOfMonth % 6 === 0) {
    materials.push({
      id: `material_${dayOfMonth}_2`,
      title: '音乐剧纪念品套装',
      location: '剧院商店',
      wantCount: Math.floor(Math.random() * 50) + 10,
      image: 'https://picsum.photos/200/200?random=' + (dayOfMonth + 400)
    })
  }

  return materials
}

const selectDate = (day: DayData) => {
  selectedDay.value = day
}

const getDayClass = (day: DayData) => {
  const classes = []

  // 空白日期
  if (day.day === 0) {
    classes.push('empty-day')
    return classes.join(' ')
  }

  if (day.isCurrentMonth) {
    classes.push('current-month')
  } else {
    classes.push('other-month')
  }

  const today = new Date().toISOString().split('T')[0]
  if (day.date === today) {
    classes.push('today')
  }

  return classes.join(' ')
}

const openSettings = () => {
  uni.navigateTo({
    url: '/pages/sub/calendar/material-manage'
  })
}

// 翻页方法 - 根据当前视图类型进行不同的翻页操作
const previousPeriod = () => {
  const newDate = new Date(currentDate.value)

  switch (calendarView.value) {
    case 'month':
      newDate.setMonth(newDate.getMonth() - 1)
      break
    case 'year':
      newDate.setFullYear(newDate.getFullYear() - 1)
      break
    case 'decade':
      newDate.setFullYear(newDate.getFullYear() - 10)
      break
  }

  currentDate.value = newDate
}

const nextPeriod = () => {
  const newDate = new Date(currentDate.value)

  switch (calendarView.value) {
    case 'month':
      newDate.setMonth(newDate.getMonth() + 1)
      break
    case 'year':
      newDate.setFullYear(newDate.getFullYear() + 1)
      break
    case 'decade':
      newDate.setFullYear(newDate.getFullYear() + 10)
      break
  }

  currentDate.value = newDate
}

const toggleAmountDisplay = () => {
  showAmount.value = !showAmount.value
}

const switchView = (view: 'month' | 'year' | 'decade') => {
  calendarView.value = view
}

const getViewTitle = () => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth() + 1

  switch (calendarView.value) {
    case 'month':
      return `${year}年${month}月`
    case 'year':
      return `${year}年`
    case 'decade':
      const decadeStart = Math.floor(year / 10) * 10
      return `${decadeStart}-${decadeStart + 9}`
    default:
      return `${year}年${month}月`
  }
}

const getViewSubtitle = () => {
  const today = new Date()
  const isCurrentPeriod = () => {
    const year = currentDate.value.getFullYear()
    const month = currentDate.value.getMonth()

    switch (calendarView.value) {
      case 'month':
        return year === today.getFullYear() && month === today.getMonth()
      case 'year':
        return year === today.getFullYear()
      case 'decade':
        const decadeStart = Math.floor(year / 10) * 10
        const currentDecadeStart = Math.floor(today.getFullYear() / 10) * 10
        return decadeStart === currentDecadeStart
      default:
        return false
    }
  }

  return isCurrentPeriod() ? '当前' : ''
}

const getStatsTitle = () => {
  switch (calendarView.value) {
    case 'month':
      return '本月统计'
    case 'year':
      return '全年统计'
    case 'decade':
      return '十年统计'
    default:
      return '统计信息'
  }
}

const formatAmount = (amount: number) => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(1) + '万'
  }
  return amount.toLocaleString()
}

const isToday = (day: any) => {
  if (!day || day.day === 0) return false
  const today = new Date().toISOString().split('T')[0]
  return day.date === today
}

const isCurrentMonth = (month: number) => {
  const today = new Date()
  const currentYear = currentDate.value.getFullYear()

  return currentYear === today.getFullYear() && month === today.getMonth() + 1
}

const isCurrentYear = (year: number) => {
  const today = new Date()
  return year === today.getFullYear()
}

const selectMonth = (month: number) => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(month - 1)
  currentDate.value = newDate
  calendarView.value = 'month'
}

const selectYear = (year: number) => {
  const newDate = new Date(currentDate.value)
  newDate.setFullYear(year)
  currentDate.value = newDate
  calendarView.value = 'year'
}

const getDecadeYears = () => {
  const currentYear = currentDate.value.getFullYear()
  const decadeStart = Math.floor(currentYear / 10) * 10
  const years = []
  for (let i = 0; i < 10; i++) {
    years.push(decadeStart + i)
  }
  return years
}

const getMonthStats = (month: number, year?: number) => {
  // 模拟月份统计数据，实际应该根据month参数查询真实数据
  const targetYear = year || currentDate.value.getFullYear()
  console.log('Getting stats for month:', month, 'year:', targetYear)
  return {
    shows: Math.floor(Math.random() * 10) + 1,
    amount: Math.floor(Math.random() * 5000) + 1000
  }
}

const getYearStats = (year: number) => {
  // 模拟年份统计数据，实际应该根据year参数查询真实数据
  console.log('Getting stats for year:', year)
  return {
    shows: Math.floor(Math.random() * 50) + 10,
    amount: Math.floor(Math.random() * 50000) + 10000
  }
}

// 获取指定月份的所有日期
const getMonthDays = (month: number) => {
  const year = currentDate.value.getFullYear()
  const daysInMonth = new Date(year, month, 0).getDate()
  const firstDay = new Date(year, month - 1, 1)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())

  const days = []
  const totalDays = Math.ceil((daysInMonth + firstDay.getDay()) / 7) * 7

  for (let i = 0; i < totalDays; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)

    const dayData = {
      date: date.toISOString().split('T')[0],
      day: date.getDate(),
      isCurrentMonth: date.getMonth() === month - 1,
      hasShow: currentMode.value === 'show' && Math.random() > 0.7,
      hasMaterial: currentMode.value === 'material' && Math.random() > 0.8
    }

    days.push(dayData)
  }

  return days
}

onLoad(() => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
})
</script>

<style lang="scss" scoped>
@import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';

/* 页面容器 - 浅色背景 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%);
  position: relative;
  padding-bottom: 120rpx; /* 为底部导航栏留出空间 */
}

/* 导航栏样式 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.nav-right {
  margin-left: 40rpx; /* 增加左边距，让切换按钮离标题更远 */
}

.nav-left {
  display: flex;
  align-items: center;
}

.nav-back-icon {
  font-size: 32rpx;
  color: #1a1a1a;
  margin-right: 16rpx;
  cursor: pointer;
  transition: color 0.3s ease;

  &:active {
    color: #9333ea;
  }
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nav-icon {
  font-size: 32rpx;
  color: #666666;
  cursor: pointer;
  transition: color 0.3s ease;

  &:active {
    color: #9333ea;
  }
}

/* 看剧/物料切换 - 分段控制器样式 */
.calendar-mode-switch {
  position: relative;
}

.segmented-control {
  position: relative;
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 4rpx;
  backdrop-filter: blur(20rpx);
  border: 1px solid rgba(147, 51, 234, 0.1);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.segment-option {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
  padding: 12rpx 20rpx;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
  min-width: 80rpx;

  &.active {
    .segment-icon {
      color: #ffffff;
      transform: scale(1.1);
    }

    .segment-text {
      color: #ffffff;
      font-weight: 600;
    }
  }

  &:not(.active) {
    &:active {
      transform: scale(0.95);
    }
  }
}

.segment-icon {
  font-size: 20rpx;
  color: #9333ea;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.segment-text {
  font-size: 22rpx;
  font-weight: 500;
  color: #9333ea;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.segment-indicator {
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  width: calc(50% - 4rpx);
  height: calc(100% - 8rpx);
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
  border-radius: 12rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
  box-shadow: 0 4rpx 16rpx rgba(147, 51, 234, 0.4);

  &.move-right {
    transform: translateX(100%);
  }
}

/* 控制栏样式 */
.control-section {
  padding: 0 24rpx 16rpx 24rpx;
}

.control-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
}

.title-section {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.current-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  background: linear-gradient(135deg, #1a1a1a 0%, #4a5568 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.current-subtitle {
  font-size: 22rpx;
  font-weight: 500;
  color: #9333ea;
  opacity: 0.8;
}

.control-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 视图切换样式 */
.view-switch {
  display: flex;
  align-items: center;
  background: rgba(147, 51, 234, 0.08);
  border-radius: 16rpx;
  padding: 6rpx;
  backdrop-filter: blur(10rpx);
}

.view-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  padding: 12rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  min-width: 60rpx;

  &.active {
    background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
    box-shadow: 0 4rpx 16rpx rgba(147, 51, 234, 0.4);
    transform: translateY(-2rpx);
  }

  &:active {
    transform: scale(0.95);
  }
}

.view-icon {
  font-size: 20rpx;
  color: #666666;
  transition: color 0.3s ease;

  .view-option.active & {
    color: #ffffff;
  }
}

.view-text {
  font-size: 20rpx;
  font-weight: 500;
  color: #666666;
  transition: color 0.3s ease;

  .view-option.active & {
    color: #ffffff;
    font-weight: 600;
  }
}

/* 统计信息内容样式 */
.stats-content {
  display: flex;
  align-items: center;
  gap: 32rpx;
  position: relative;
  z-index: 2;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;

  &.primary {
    .stats-number {
      color: #ffffff;
    }
  }

  &.secondary {
    .stats-number {
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

.stats-value {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
  margin-bottom: 8rpx;
}

.stats-number {
  font-size: 36rpx;
  font-weight: 700;
  line-height: 1;
}

.stats-unit {
  font-size: 20rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.stats-label {
  font-size: 22rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
}

.amount-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);

  .fas {
    font-size: 20rpx;
    color: #ffffff;
  }

  &:active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0.95);
  }
}

/* 导航按钮样式 */
.nav-buttons {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56rpx;
  height: 56rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);

  .fas {
    font-size: 24rpx;
    color: #4a5568;
    transition: color 0.3s ease;
  }

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  }

  &:active {
    transform: scale(0.95);
  }

  &.prev:active {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    .fas {
      color: #ffffff;
    }
  }

  &.next:active {
    background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
    .fas {
      color: #ffffff;
    }
  }
}

/* 统计信息卡片样式 */
.stats-section {
  padding: 0 24rpx 24rpx 24rpx;
}

.stats-card {
  background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 12rpx 40rpx rgba(147, 51, 234, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
  }
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
  position: relative;
  z-index: 2;
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  backdrop-filter: blur(10rpx);

  .fas {
    font-size: 24rpx;
    color: #ffffff;
  }
}

.stats-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  flex: 1;
  margin-left: 16rpx;
}

/* 日历容器样式 */
.calendar-container {
  margin: 0 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
}

/* 月视图样式 */
.month-view {
  width: 100%;
}

.weekdays-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.weekday-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border: 1px solid #f1f5f9;
}

.weekday-text {
  font-size: 22rpx;
  font-weight: 600;
  color: #64748b;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
}

.calendar-grid {
  width: 100%;
}

.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 8rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.calendar-day {
  position: relative;
  height: 150rpx;
  background: #ffffff;
  border: 1px solid #f1f5f9;
  border-radius: 12rpx;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: #e2e8f0;
  }

  &:active {
    transform: scale(0.98);
  }

  /* 当前月份的日期 */
  &.current-month {
    background: #ffffff;
    border-color: #e2e8f0;
  }

  /* 非当前月份的日期 */
  &.other-month {
    background: #fafbfc;
    opacity: 0.4;
    border-color: #f3f4f6;
  }

  /* 空白日期 */
  &.empty-day {
    background: transparent;
    border: none;
    cursor: default;
    pointer-events: none;
  }
}

/* 日期数字样式 */
.day-number {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  pointer-events: none;
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  backdrop-filter: blur(8rpx);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.day-text {
  font-size: 20rpx;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;

  &.current-month {
    color: #374151;
  }

  &.other-month {
    color: #9ca3af;
  }
}

/* 演出内容样式 */
.show-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 单个剧目：满铺 */
.single-show {
  width: 100%;
  height: 100%;
  position: relative;
}

.show-poster {
  width: 100%;
  height: 100%;

  &.full {
    border-radius: 12rpx;
  }

  &.split {
    border-radius: 0;
  }
}

/* 多个剧目：上下平分 */
.multiple-shows {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.show-item {
  flex: 1;
  position: relative;

  &.top {
    border-bottom: 2px solid rgba(255, 255, 255, 0.8);

    .show-poster {
      border-radius: 12rpx 12rpx 0 0;
    }
  }

  &.bottom {
    .show-poster {
      border-radius: 0 0 12rpx 12rpx;
    }
  }
}

.more-shows {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  background: rgba(0, 0, 0, 0.75);
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  z-index: 10;
  backdrop-filter: blur(10rpx);
}

.more-text {
  font-size: 18rpx;
  font-weight: 600;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
}

/* 物料标记样式 */
.material-mark {
  position: absolute;
  bottom: 6rpx;
  right: 6rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
  background: #10b981;
  border-radius: 8rpx;
  padding: 4rpx 6rpx;
  z-index: 5;
}

.material-dot {
  width: 6rpx;
  height: 6rpx;
  background: #ffffff;
  border-radius: 50%;
}

.material-count {
  font-size: 16rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 演出数量指示器 */
.show-count-badge {
  position: absolute;
  bottom: 6rpx;
  right: 6rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 12rpx;
  padding: 2rpx 8rpx;
  z-index: 15;
}

.count-text {
  font-size: 18rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 观看状态指示器 */
.watch-status {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  display: flex;
  flex-direction: column;
  gap: 2rpx;
  z-index: 15;
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;

  &.watched {
    background: #22c55e;
    box-shadow: 0 0 4rpx rgba(34, 197, 94, 0.5);
  }

  &.unwatched {
    background: #eab308;
    box-shadow: 0 0 4rpx rgba(234, 179, 8, 0.5);
  }
}

/* 物料指示器 */
.material-indicator {
  position: absolute;
  bottom: 6rpx;
  right: 6rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
  background: rgba(147, 51, 234, 0.9);
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  z-index: 15;
}

.material-dot {
  width: 8rpx;
  height: 8rpx;
  background: #ffffff;
  border-radius: 50%;
}

.material-count {
  font-size: 18rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 年视图样式 */
.year-view {
  width: 100%;
}

.year-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.month-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12rpx;
  padding: 16rpx;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }
}

.month-header {
  margin-bottom: 20rpx;
}

.month-title-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.month-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}

.month-indicator {
  width: 16rpx;
  height: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &.current {
    .fas {
      font-size: 8rpx;
      color: #9333ea;
    }
  }
}

/* 月份小日历 */
.month-calendar {
  width: 100%;
}

.mini-calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2rpx;
}

.mini-day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 4rpx;
  transition: all 0.2s ease;

  &.current-month {
    background: #ffffff;
    border: 1px solid #e2e8f0;
  }

  &.has-show {
    background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
    border: 1px solid #c7d2fe;

    .mini-day-text {
      color: #4338ca;
      font-weight: 600;
    }
  }

  &.has-material {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border: 1px solid #bbf7d0;

    .mini-day-text {
      color: #166534;
      font-weight: 600;
    }
  }

  &.today {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 1px solid #f59e0b;
    box-shadow: 0 0 0 1px rgba(245, 158, 11, 0.2);

    .mini-day-text {
      color: #92400e;
      font-weight: 700;
    }
  }
}

.mini-day-text {
  font-size: 18rpx;
  color: #64748b;
  font-weight: 500;
}

/* 十年视图样式 */
.decade-view {
  width: 100%;
}

.decade-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.year-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12rpx;
  padding: 16rpx;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }
}

.year-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.year-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}

.current-year-indicator {
  width: 20rpx;
  height: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .fas {
    font-size: 12rpx;
    color: #f59e0b;
  }
}

/* 年份月份网格 */
.year-months {
  width: 100%;
}

.months-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 6rpx;
}

.month-item {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 6rpx;
  transition: all 0.2s ease;

  &.has-activity {
    background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
    border: 1px solid #c7d2fe;
    box-shadow: 0 1px 3px rgba(99, 102, 241, 0.1);

    .month-text {
      color: #4338ca;
      font-weight: 600;
    }
  }

  &.current {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 1px solid #f59e0b;
    box-shadow: 0 0 0 1px rgba(245, 158, 11, 0.2);

    .month-text {
      color: #92400e;
      font-weight: 700;
    }
  }
}

.month-text {
  font-size: 18rpx;
  color: #64748b;
  font-weight: 500;
}

/* 动画效果 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
  25% {
    opacity: 0.7;
    transform: rotate(90deg) scale(1.1);
  }
  50% {
    opacity: 1;
    transform: rotate(180deg) scale(1);
  }
  75% {
    opacity: 0.7;
    transform: rotate(270deg) scale(1.1);
  }
}

/* 选中日期详情样式 */
.selected-day-details {
  margin: 16rpx 24rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
}

.details-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid #f1f5f9;
}

.details-date {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background: #f8fafc;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    background: #f1f5f9;
    transform: scale(0.95);
  }
}

.close-icon {
  font-size: 20rpx;
  color: #64748b;
}

/* 演出详情项样式 */
.show-item-detail {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #fafbfc;
  border-radius: 12rpx;
  border: 1px solid #f1f5f9;

  &:last-child {
    margin-bottom: 0;
  }
}

.show-poster-detail {
  width: 80rpx;
  height: 100rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.show-info {
  flex: 1;
}

.show-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: block;
}

.show-meta {
  margin-bottom: 6rpx;
}

.show-theater {
  font-size: 24rpx;
  color: #64748b;
}

.show-meta-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.show-time {
  font-size: 24rpx;
  color: #64748b;
}

.show-price {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

.show-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 8rpx;
}

.watch-status {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;

  &.watched {
    background: rgba(34, 197, 94, 0.1);

    .status-text {
      color: #16a34a;
    }
  }

  &.unwatched {
    background: rgba(245, 158, 11, 0.1);

    .status-text {
      color: #d97706;
    }
  }
}

.status-text {
  font-size: 20rpx;
  font-weight: 500;
}

/* 空白日期装饰 */
.empty-day-decoration {
  position: absolute;
  bottom: 12rpx;
  right: 12rpx;
  z-index: 2;
}

.decoration-dot {
  width: 6rpx;
  height: 6rpx;
  background: #e2e8f0;
  border-radius: 50%;
  opacity: 0.6;
}

/* 物料详情项样式 */
.material-item-detail {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #fafbfc;
  border-radius: 12rpx;
  border: 1px solid #f1f5f9;
  cursor: pointer;
  transition: all 0.2s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    background: #f1f5f9;
    transform: scale(0.98);
  }
}

.material-image-detail {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.material-info {
  flex: 1;
}

.material-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
  margin-bottom: 8rpx;
  display: block;
}

.material-meta {
  margin-bottom: 8rpx;
}

.material-location {
  font-size: 24rpx;
  color: #64748b;
}

.material-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.want-count {
  font-size: 22rpx;
  color: #10b981;
  font-weight: 500;
}

.material-arrow {
  font-size: 16rpx;
  color: #9ca3af;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 32rpx 0;
}

.empty-text {
  font-size: 26rpx;
  color: #9ca3af;
}
</style>
